from backtesting import Strategy
from backtesting.lib import FractionalBacktest
from backtesting.test import BTCUSD
from json import loads, dumps

BACKTEST_STORAGE_FILENAME = 'backtests.json'
BACKTESTS_STORAGE_DIR = 'backtests'

def get_backtest_json() -> dict:
    try:
        with open(BACKTEST_STORAGE_FILENAME, 'r') as f:
            return loads(f.read())
    except FileNotFoundError:
        return {}


def write_backtest_json(_json: dict) -> None:
    with open(BACKTEST_STORAGE_FILENAME, 'w') as f:
        f.write(dumps(_json))


def get_top_10_backtest_json(backtest_json) -> dict:
    return {k: v for k, v in sorted(backtest_json.items(), key=lambda item: item[1]['Return [%]'], reverse=True)[:10]}


def store_individual_backtest(backtest_str: str, filename: str) -> None:
    with open(f'{BACKTESTS_STORAGE_DIR}/{filename}.json', 'w+') as f:
        f.write(backtest_str)

def run_backtest(strat: Strategy):
    if not hasattr(strat, 'name'):
        raise AttributeError('Strategy must have a name')
    
    bt = FractionalBacktest(BTCUSD, strat, cash=10000, commission=0.02)
    stats = bt.run()

    backtest_json = get_backtest_json()
    backtest_no = str(len(backtest_json))

    stats_json_str = stats.to_json()
    store_individual_backtest(stats_json_str, strat.name + '_' + backtest_no)

    data = loads(stats_json_str)
    data.pop('_strategy')

    backtest_json = get_backtest_json()
    backtest_no = str(len(backtest_json))
    backtest_json[strat.name + '_' + backtest_no] = {k: v for k, v in data.items() if k in ['Equity Final [$]', 'Equity Peak [$]', 'Return [%]', 'Max. Drawdown [%]', 'Max. Drawdown Duration', 'Sharpe Ratio']}

    top_10 = get_top_10_backtest_json(backtest_json)
    print("This backtest:")
    print("Name, Equity Final, Equity Peak, Return [%], Max Drawdown [%], Max. Drawdown Duration, Sharpe Ratio")
    print(f"{strat.name}, {data['Equity Final [$]']}, {data['Equity Peak [$]']}, {data['Return [%]']}, {data['Max. Drawdown [%]']}, {data['Max. Drawdown Duration']}, {data['Sharpe Ratio']}")

    print("\nBest 10 backtests:")
    print("#, Name, Equity Final, Equity Peak, Return [%], Max Drawdown [%], Max. Drawdown Duration, Sharpe Ratio")
    for i, (k, v) in enumerate(top_10.items()):
        print(f"{i+1}. {k}, {v['Equity Final [$]']}, {v['Equity Peak [$]']}, {v['Return [%]']}, {v['Max. Drawdown [%]']}, {v['Max. Drawdown Duration']}, {v['Sharpe Ratio']}")
    write_backtest_json(backtest_json)


if __name__ == '__main__':
    from strats.macd_htf_ltf import MACDMomentumHTF
    run_backtest(MACDMomentumHTF)