# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Python backtesting framework for trading strategies using the `backtesting` library. The project focuses on testing and comparing different trading strategies against BTCUSD data.

## Development Commands

- **Install dependencies**: `uv sync`
- **Run main backtest**: `python main.py`
- **Run specific strategy**: Import and call `run_backtest(StrategyClass)` in main.py

## Architecture

### Core Components

- **main.py**: Central backtesting engine that:
  - Runs backtests using FractionalBacktest with BTCUSD data
  - Stores results in backtests.json
  - Displays current backtest results and top 10 performers
  - Uses 10000 cash with 0.02 commission

- **strats/**: Strategy implementations directory
  - Each strategy must inherit from `backtesting.Strategy`
  - Must include a `name` attribute
  - Must implement `init()` and `next()` methods

- **backtests.json**: JSON storage for backtest results and performance metrics

### Strategy Development Pattern

1. Create new strategy class in `strats/` directory
2. Inherit from `backtesting.Strategy`
3. Set class-level `name` attribute
4. Implement `init()` method for indicator setup using `self.I()`
5. Implement `next()` method for trading logic
6. Import and run via `run_backtest()` function

### Data Format

The framework expects strategies to work with OHLC data accessible via `self.data.Close`, `self.data.Open`, etc.

## Key Dependencies

- **backtesting>=0.6.5**: Core backtesting framework
- Uses `backtesting.lib.crossover` for signal detection
- Uses `backtesting.test.SMA` for moving averages
- Default dataset: `backtesting.test.BTCUSD`