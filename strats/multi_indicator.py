from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd
import numpy as np


def RSI(close, n=14):
    """Calculate RSI indicator"""
    close = pd.Series(close)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.values


def MACD(close, fast=12, slow=26, signal=9):
    """Calculate MACD line and signal"""
    close = pd.Series(close)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    return macd_line.values, signal_line.values


def SMA(close, n):
    """Simple Moving Average"""
    close = pd.Series(close)
    return close.rolling(window=n).mean().values


class MultiIndicatorStrategy(Strategy):
    name = 'Multi-Indicator Combo'
    rsi_period = 14
    rsi_oversold = 35  # More conservative than 30
    rsi_overbought = 70
    sma_short = 20
    sma_long = 50
    stop_loss_pct = 0.10
    position_size_pct = 0.8  # Use 80% of available capital
    
    def init(self):
        close = self.data.Close
        self.rsi = self.I(RSI, close, self.rsi_period)
        self.macd, self.signal = self.I(MACD, close)
        self.sma_short = self.I(SMA, close, self.sma_short)
        self.sma_long = self.I(SMA, close, self.sma_long)

    def next(self):
        # Multiple confirmation signals
        rsi_oversold = self.rsi[-1] < self.rsi_oversold
        macd_bullish = self.macd[-1] > self.signal[-1]
        price_above_sma_short = self.data.Close[-1] > self.sma_short[-1]
        short_above_long = self.sma_short[-1] > self.sma_long[-1]
        
        # Entry: Multiple confirmations required
        if (rsi_oversold and 
            macd_bullish and 
            price_above_sma_short and 
            short_above_long and 
            not self.position):
            
            # Position sizing and risk management
            entry_price = self.data.Close[-1]
            stop_loss = entry_price * (1 - self.stop_loss_pct)
            self.buy(size=self.position_size_pct, sl=stop_loss)
            
        # Exit conditions: Any bearish signal
        elif self.position and (
            self.rsi[-1] > self.rsi_overbought or
            crossover(self.signal, self.macd) or
            self.data.Close[-1] < self.sma_short[-1]
        ):
            self.position.close()