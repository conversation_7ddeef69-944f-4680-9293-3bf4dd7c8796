from backtesting import Strategy
import pandas as pd
import numpy as np


def StochasticRSI(close, rsi_period=14, stoch_period=14):
    """Calculate Stochastic RSI"""
    close = pd.Series(close)
    
    # Calculate RSI first
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # Calculate Stochastic of RSI
    rsi_min = rsi.rolling(window=stoch_period).min()
    rsi_max = rsi.rolling(window=stoch_period).max()
    stoch_rsi = (rsi - rsi_min) / (rsi_max - rsi_min) * 100
    
    return stoch_rsi.values


class StochasticRSIStrategy(Strategy):
    name = 'Stochastic RSI'
    rsi_period = 14
    stoch_period = 14
    oversold_threshold = 20
    overbought_threshold = 80

    def init(self):
        close = self.data.Close
        self.stoch_rsi = self.I(StochasticRSI, close, self.rsi_period, self.stoch_period)

    def next(self):
        if self.stoch_rsi[-1] < self.oversold_threshold and not self.position:
            self.buy()
        elif self.stoch_rsi[-1] > self.overbought_threshold and self.position:
            self.position.close()