from backtesting import Strategy
import pandas as pd
import numpy as np


def RSI(close, n=14):
    """Calculate RSI indicator"""
    close = pd.Series(close)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.values


class RSIOptimized(Strategy):
    name = 'RSI Optimized'
    rsi_period = 14
    oversold_threshold = 25  # More conservative entry
    overbought_threshold = 75  # Earlier exit
    stop_loss_pct = 0.15  # 15% stop loss for risk management
    take_profit_pct = 0.30  # 30% take profit to lock in gains

    def init(self):
        close = self.data.Close
        self.rsi = self.I(RSI, close, self.rsi_period)

    def next(self):
        # Entry condition: RSI oversold
        if self.rsi[-1] < self.oversold_threshold and not self.position:
            # Set stop loss and take profit levels
            entry_price = self.data.Close[-1]
            stop_loss = entry_price * (1 - self.stop_loss_pct)
            take_profit = entry_price * (1 + self.take_profit_pct)
            
            self.buy(sl=stop_loss, tp=take_profit)
            
        # Exit condition: RSI overbought or trailing stop
        elif self.rsi[-1] > self.overbought_threshold and self.position:
            self.position.close()
            
        # Additional risk management: exit on high RSI even if not overbought
        elif self.rsi[-1] > 85 and self.position:
            self.position.close()