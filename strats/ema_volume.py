from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd


def EMA(close, n):
    """Calculate Exponential Moving Average"""
    close = pd.Series(close)
    return close.ewm(span=n).mean().values


def VolumeMA(volume, n):
    """Calculate Volume Moving Average"""
    volume = pd.Series(volume)
    return volume.rolling(window=n).mean().values


class EMAVolumeStrategy(Strategy):
    name = 'EMA Volume Crossover'
    fast_ema = 12
    slow_ema = 26
    volume_ma_period = 20
    volume_multiplier = 1.5

    def init(self):
        close = self.data.Close
        volume = self.data.Volume
        self.ema_fast = self.I(EMA, close, self.fast_ema)
        self.ema_slow = self.I(EMA, close, self.slow_ema)
        self.volume_ma = self.I(VolumeMA, volume, self.volume_ma_period)

    def next(self):
        volume_confirmed = self.data.Volume[-1] > self.volume_ma[-1] * self.volume_multiplier
        
        if crossover(self.ema_fast, self.ema_slow) and volume_confirmed and not self.position:
            self.buy()
        elif crossover(self.ema_slow, self.ema_fast) and self.position:
            self.position.close()