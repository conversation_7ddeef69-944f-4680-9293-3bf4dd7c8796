from backtesting import Strategy
import pandas as pd


def BollingerBands(close, n=20, std_dev=2):
    """Calculate Bollinger Bands"""
    close = pd.Series(close)
    sma = close.rolling(window=n).mean()
    std = close.rolling(window=n).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return sma.values, upper_band.values, lower_band.values


class BollingerBandsStrategy(Strategy):
    name = 'Bollinger Bands'
    bb_period = 20
    bb_std = 2

    def init(self):
        close = self.data.Close
        self.sma, self.upper_band, self.lower_band = self.I(BollingerBands, close, self.bb_period, self.bb_std)

    def next(self):
        if self.data.Close[-1] < self.lower_band[-1] and not self.position:
            self.buy()
        elif self.data.Close[-1] > self.upper_band[-1] and self.position:
            self.position.close()