from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd


def MACD(close, fast=12, slow=26, signal=9):
    """Calculate MACD, signal line, and histogram"""
    close = pd.Series(close)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line.values, signal_line.values, histogram.values


def SMA(close, n):
    """Simple Moving Average for trend filter"""
    close = pd.Series(close)
    return close.rolling(window=n).mean().values


class MACDOptimized(Strategy):
    name = 'MACD Optimized'
    fast_period = 12
    slow_period = 26
    signal_period = 9
    trend_filter_period = 50  # Only trade in direction of trend
    stop_loss_pct = 0.08  # Tighter stop loss 
    min_macd_threshold = 0.00001  # Avoid weak signals

    def init(self):
        close = self.data.Close
        self.macd, self.signal, self.histogram = self.I(MACD, close, self.fast_period, self.slow_period, self.signal_period)
        self.trend_sma = self.I(SMA, close, self.trend_filter_period)

    def next(self):
        # Only trade when price is above long-term trend (bullish bias)
        price_above_trend = self.data.Close[-1] > self.trend_sma[-1]
        
        # Strong signal filter - avoid weak MACD crossovers
        strong_signal = abs(self.macd[-1]) > self.min_macd_threshold
        
        # Entry: MACD bullish crossover with trend and strength filters
        if (crossover(self.macd, self.signal) and 
            price_above_trend and 
            strong_signal and 
            not self.position):
            
            # Risk management with stop loss
            entry_price = self.data.Close[-1]
            stop_loss = entry_price * (1 - self.stop_loss_pct)
            self.buy(sl=stop_loss)
            
        # Exit: MACD bearish crossover or trend reversal
        elif ((crossover(self.signal, self.macd) or 
               not price_above_trend) and 
              self.position):
            self.position.close()
            
        # Additional exit: take profit on strong momentum
        elif (self.position and 
              self.histogram[-1] > self.histogram[-2] * 2 and  # Momentum weakening
              self.macd[-1] > 0):
            self.position.close()