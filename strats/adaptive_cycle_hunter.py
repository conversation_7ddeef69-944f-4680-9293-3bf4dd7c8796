from backtesting import Strategy
import pandas as pd
import numpy as np
from datetime import datetime


def CyclePhaseDetector(close, volume, cycle_length=1460):  # ~4 years in days
    """Detect Bitcoin halving cycle phases"""
    close_series = pd.Series(close)
    volume_series = pd.Series(volume)
    
    # Price momentum over different timeframes
    momentum_short = close_series.pct_change(30).rolling(30).mean()  # 1 month
    momentum_medium = close_series.pct_change(90).rolling(60).mean()  # 3 months
    momentum_long = close_series.pct_change(365).rolling(90).mean()  # 1 year
    
    # Volume analysis
    volume_ma = volume_series.rolling(60).mean()
    volume_ratio = volume_series / volume_ma
    
    # Volatility analysis
    returns = close_series.pct_change()
    volatility = returns.rolling(60).std()
    vol_percentile = volatility.rolling(365).rank(pct=True)
    
    # Cycle phase scoring (0=accumulation, 1=markup, 2=distribution, 3=markdown)
    phase_score = (
        momentum_short * 0.4 +
        momentum_medium * 0.4 +
        momentum_long * 0.2
    )
    
    # Phase classification
    phases = pd.Series(index=close_series.index, dtype=float)
    
    # Accumulation: Low momentum, high volume, high volatility
    accumulation_mask = (
        (phase_score < -0.05) & 
        (vol_percentile > 0.6) & 
        (volume_ratio > 1.2)
    )
    
    # Markup: Strong positive momentum, increasing volume
    markup_mask = (
        (phase_score > 0.02) & 
        (momentum_medium > 0.05) & 
        (volume_ratio > 1.0)
    )
    
    # Distribution: High prices but weakening momentum
    distribution_mask = (
        (phase_score < 0.05) & 
        (phase_score > -0.02) &
        (momentum_short < momentum_medium) &
        (vol_percentile > 0.7)
    )
    
    # Default to markdown for other conditions
    phases[accumulation_mask] = 0  # Accumulation
    phases[markup_mask] = 1       # Markup
    phases[distribution_mask] = 2  # Distribution
    phases[~(accumulation_mask | markup_mask | distribution_mask)] = 3  # Markdown
    
    return phases.values


def RSI(close, n=14):
    """RSI calculation"""
    close_series = pd.Series(close)
    delta = close_series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    return (100 - (100 / (1 + rs))).values


def MACD(close, fast=12, slow=26, signal=9):
    """MACD calculation"""
    close_series = pd.Series(close)
    ema_fast = close_series.ewm(span=fast).mean()
    ema_slow = close_series.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    return macd_line.values, signal_line.values


class AdaptiveCycleHunter(Strategy):
    name = 'Adaptive Cycle Hunter'
    
    # RSI parameters
    rsi_period = 14
    
    # MACD parameters
    macd_fast = 12
    macd_slow = 26
    macd_signal = 9
    
    # Phase-specific parameters
    accumulation_rsi_buy = 25
    accumulation_rsi_sell = 75
    markup_rsi_buy = 45
    markup_rsi_sell = 75
    distribution_rsi_sell = 70
    
    # Risk management
    base_position_size = 0.7
    stop_loss_pct = 0.15

    def init(self):
        close = self.data.Close
        volume = self.data.Volume
        
        # Cycle detection system
        self.cycle_phase = self.I(CyclePhaseDetector, close, volume)
        
        # Technical indicators
        self.rsi = self.I(RSI, close, self.rsi_period)
        self.macd, self.macd_signal = self.I(MACD, close, 
                                           self.macd_fast, self.macd_slow, self.macd_signal)

    def next(self):
        # Get current cycle phase
        current_phase = self.cycle_phase[-1] if not np.isnan(self.cycle_phase[-1]) else 1
        current_rsi = self.rsi[-1] if not np.isnan(self.rsi[-1]) else 50
        current_macd = self.macd[-1] if not np.isnan(self.macd[-1]) else 0
        current_macd_signal = self.macd_signal[-1] if not np.isnan(self.macd_signal[-1]) else 0
        
        # Phase-adaptive trading logic
        if current_phase == 0:  # Accumulation Phase
            self._accumulation_logic(current_rsi, current_macd, current_macd_signal)
        elif current_phase == 1:  # Markup Phase
            self._markup_logic(current_rsi, current_macd, current_macd_signal)
        elif current_phase == 2:  # Distribution Phase
            self._distribution_logic(current_rsi)
        else:  # Markdown Phase
            self._markdown_logic()

    def _accumulation_logic(self, rsi, macd, macd_signal):
        """Aggressive mean reversion during accumulation"""
        if not self.position:
            # Aggressive buying on deep oversold conditions
            if rsi < self.accumulation_rsi_buy and macd > macd_signal:
                entry_price = self.data.Close[-1]
                stop_loss = entry_price * (1 - self.stop_loss_pct * 1.5)  # Wider stops
                self.buy(size=self.base_position_size * 1.2, sl=stop_loss)
        
        elif self.position:
            # Take profits on overbought conditions
            if rsi > self.accumulation_rsi_sell:
                self.position.close()

    def _markup_logic(self, rsi, macd, macd_signal):
        """Momentum following during markup"""
        if not self.position:
            # Buy on momentum with moderate RSI
            if (rsi > self.markup_rsi_buy and rsi < 65 and 
                macd > macd_signal and macd > 0):
                entry_price = self.data.Close[-1]
                stop_loss = entry_price * (1 - self.stop_loss_pct)  # Standard stops
                self.buy(size=self.base_position_size, sl=stop_loss)
        
        elif self.position:
            # Exit on overbought or momentum loss
            if rsi > self.markup_rsi_sell or macd < macd_signal:
                self.position.close()

    def _distribution_logic(self, rsi):
        """Early profit taking during distribution"""
        if not self.position:
            # Very selective entries - only on extreme oversold
            if rsi < 20:
                entry_price = self.data.Close[-1]
                stop_loss = entry_price * (1 - self.stop_loss_pct * 0.8)  # Tighter stops
                self.buy(size=self.base_position_size * 0.6, sl=stop_loss)
        
        elif self.position:
            # Early profit taking
            if rsi > self.distribution_rsi_sell:
                self.position.close()

    def _markdown_logic(self):
        """Capital preservation during markdown"""
        # Close any existing positions
        if self.position:
            self.position.close()
        
        # No new positions during markdown phase