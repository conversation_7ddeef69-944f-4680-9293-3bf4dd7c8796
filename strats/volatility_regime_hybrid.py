from backtesting import Strategy
import pandas as pd
import numpy as np


def RSI(close, n=14):
    """Calculate RSI indicator"""
    close = pd.Series(close)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.values


def MACD(close, fast=12, slow=26, signal=9):
    """Calculate MACD, signal line, and histogram"""
    close = pd.Series(close)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line.values, signal_line.values, histogram.values


def ATR(high, low, close, n=14):
    """Average True Range for volatility measurement"""
    high, low, close = pd.Series(high), pd.Series(low), pd.Series(close)
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window=n).mean()
    return atr.values


def SMA(close, n):
    """Simple Moving Average"""
    close = pd.Series(close)
    return close.rolling(window=n).mean().values


class VolatilityRegimeHybrid(Strategy):
    """
    Advanced hybrid strategy that adapts based on volatility regimes:
    - High volatility: Uses momentum (MACD) with tight stops
    - Low volatility: Uses mean reversion (RSI) with wider targets
    - Combines multi-timeframe analysis with dynamic position sizing
    """
    name = 'Volatility Regime Hybrid'
    
    # Parameters
    rsi_period = 14
    macd_fast = 8  # Faster for crypto volatility
    macd_slow = 21
    macd_signal = 5
    atr_period = 14
    volatility_lookback = 24  # 2 years of monthly data
    trend_filter_period = 36  # 3 years for major trend
    
    # Regime thresholds (percentiles of historical volatility)
    high_vol_threshold = 75
    low_vol_threshold = 25
    
    # Risk management
    base_stop_loss = 0.25
    base_take_profit = 0.50
    vol_adjustment_factor = 2.0

    def init(self):
        close = self.data.Close
        high = self.data.High
        low = self.data.Low
        
        # Technical indicators
        self.rsi = self.I(RSI, close, self.rsi_period)
        self.macd, self.macd_signal, self.macd_hist = self.I(
            MACD, close, self.macd_fast, self.macd_slow, self.macd_signal
        )
        self.atr = self.I(ATR, high, low, close, self.atr_period)
        self.trend_sma = self.I(SMA, close, self.trend_filter_period)
        
    def next(self):
        if len(self.data) < self.volatility_lookback:
            return
            
        # Calculate volatility regime
        recent_returns = pd.Series(self.data.Close).pct_change().rolling(self.volatility_lookback).std()
        vol_percentile = recent_returns.rolling(self.volatility_lookback).rank(pct=True).iloc[-1] * 100
        
        # Determine regime
        is_high_vol = vol_percentile > self.high_vol_threshold
        is_low_vol = vol_percentile < self.low_vol_threshold
        
        # Trend filter
        in_uptrend = self.data.Close[-1] > self.trend_sma[-1]
        current_price = self.data.Close[-1]
        
        # Dynamic risk management based on volatility
        vol_mult = max(0.5, 1 + (vol_percentile / 100) * self.vol_adjustment_factor)
        stop_loss_pct = max(0.05, self.base_stop_loss / vol_mult)
        take_profit_pct = min(2.0, self.base_take_profit * vol_mult)
        
        if not self.position and in_uptrend:
            entry_signal = False
            
            if is_high_vol:
                # High volatility: Momentum strategy with MACD
                if (self.macd[-1] > self.macd_signal[-1] and 
                    self.macd[-2] <= self.macd_signal[-2] and
                    self.macd[-1] > 0):  # Only long when MACD is positive
                    entry_signal = True
                    
            elif is_low_vol:
                # Low volatility: Mean reversion with RSI
                if self.rsi[-1] < 35 and self.rsi[-1] > 20:  # Avoid knife-catching
                    entry_signal = True
                    
            else:  # Medium volatility
                # Hybrid approach: Both momentum and mean reversion signals
                momentum_signal = (self.macd[-1] > self.macd_signal[-1] and 
                                 self.macd_hist[-1] > self.macd_hist[-2])
                mean_reversion_signal = (self.rsi[-1] < 40 and self.rsi[-1] > 25)
                
                if momentum_signal or mean_reversion_signal:
                    entry_signal = True
            
            if entry_signal:
                stop_loss = current_price * (1 - stop_loss_pct)
                take_profit = current_price * (1 + take_profit_pct)
                self.buy(sl=stop_loss, tp=take_profit)
                
        elif self.position:
            # Exit conditions based on regime
            if is_high_vol:
                # Quick exits in high volatility
                if (self.macd[-1] < self.macd_signal[-1] or 
                    self.rsi[-1] > 80):
                    self.position.close()
                    
            elif is_low_vol:
                # Patient exits in low volatility
                if self.rsi[-1] > 70:
                    self.position.close()
                    
            else:  # Medium volatility
                # Balanced exit approach
                if (self.rsi[-1] > 75 or 
                    (self.macd[-1] < self.macd_signal[-1] and self.rsi[-1] > 60)):
                    self.position.close()