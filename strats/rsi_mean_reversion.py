from backtesting import Strategy
import pandas as pd
import numpy as np


def RSI(close, n=14):
    """Calculate RSI indicator"""
    close = pd.Series(close)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.values


class RSIMeanReversion(Strategy):
    name = 'RSI Mean Reversion'
    rsi_period = 14
    oversold_threshold = 30
    overbought_threshold = 70

    def init(self):
        close = self.data.Close
        self.rsi = self.I(RSI, close, self.rsi_period)

    def next(self):
        if self.rsi[-1] < self.oversold_threshold and not self.position:
            self.buy()
        elif self.rsi[-1] > self.overbought_threshold and self.position:
            self.position.close()