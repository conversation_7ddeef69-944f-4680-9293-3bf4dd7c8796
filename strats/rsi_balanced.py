from backtesting import Strategy
import pandas as pd
import numpy as np


def RSI(close, n=14):
    """Calculate RSI indicator"""
    close = pd.Series(close)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.values


def SMA(close, n):
    """Simple Moving Average for trend filter"""
    close = pd.Series(close)
    return close.rolling(window=n).mean().values


class RSIBalanced(Strategy):
    name = 'RSI Balanced'
    rsi_period = 14
    oversold_threshold = 30
    overbought_threshold = 70
    trend_period = 100  # Long-term trend filter
    stop_loss_pct = 0.20  # 20% stop loss
    take_profit_mult = 2.5  # 2.5x risk/reward ratio

    def init(self):
        close = self.data.Close
        self.rsi = self.I(RSI, close, self.rsi_period)
        self.trend_sma = self.I(SMA, close, self.trend_period)

    def next(self):
        # Only trade when price is above long-term trend (bull market filter)
        in_uptrend = self.data.Close[-1] > self.trend_sma[-1]
        
        if self.rsi[-1] < self.oversold_threshold and in_uptrend and not self.position:
            # Calculate risk management levels
            entry_price = self.data.Close[-1]
            stop_loss = entry_price * (1 - self.stop_loss_pct)
            take_profit = entry_price * (1 + self.stop_loss_pct * self.take_profit_mult)
            
            self.buy(sl=stop_loss, tp=take_profit)
            
        elif self.rsi[-1] > self.overbought_threshold and self.position:
            self.position.close()