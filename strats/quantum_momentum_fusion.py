from backtesting import Strategy
import pandas as pd
import numpy as np


def QuantumMomentum(close, volume, fast=8, medium=21, slow=55):
    """Quantum Momentum Score with non-linear interactions"""
    close_series = pd.Series(close)
    volume_series = pd.Series(volume)
    
    # Multi-timeframe momentum
    returns_1m = close_series.pct_change(1)
    returns_3m = close_series.pct_change(3)
    returns_6m = close_series.pct_change(6)
    
    # Momentum calculations
    momentum_1m = returns_1m.rolling(fast).mean()
    momentum_3m = returns_3m.rolling(medium).mean()
    momentum_6m = returns_6m.rolling(slow).mean()
    
    # Volatility adjustment
    volatility = returns_3m.rolling(21).std()
    vol_adjusted_momentum = momentum_3m / (volatility + 0.001)
    
    # Volume-weighted momentum
    vwap_momentum = ((returns_3m * volume_series).rolling(12).sum() / 
                     volume_series.rolling(12).sum())
    
    # Price acceleration
    acceleration = momentum_3m.diff(3)
    
    # Non-linear momentum interactions
    momentum_interaction = np.sign(momentum_1m * momentum_3m * momentum_6m) * np.abs(
        momentum_1m * momentum_3m * momentum_6m) ** (1/3)
    
    # Quantum score fusion
    quantum_score = (
        momentum_3m * 0.3 +
        vol_adjusted_momentum * 0.25 +
        vwap_momentum * 0.2 +
        acceleration * 0.15 +
        momentum_interaction * 0.1
    )
    
    return quantum_score.values


def ATR(high, low, close, n=14):
    """Average True Range for adaptive stops"""
    high_series = pd.Series(high)
    low_series = pd.Series(low) 
    close_series = pd.Series(close)
    
    tr1 = high_series - low_series
    tr2 = np.abs(high_series - close_series.shift())
    tr3 = np.abs(low_series - close_series.shift())
    
    tr = np.maximum(tr1, np.maximum(tr2, tr3))
    return tr.rolling(n).mean().values


def VolatilityRegime(close, n=21):
    """Detect volatility regime"""
    close_series = pd.Series(close)
    returns = close_series.pct_change()
    volatility = returns.rolling(n).std()
    vol_percentile = volatility.rolling(252).rank(pct=True)
    return vol_percentile.values


class QuantumMomentumFusion(Strategy):
    name = 'Quantum Momentum Fusion'
    
    # Quantum parameters
    quantum_threshold_high = 0.002
    quantum_threshold_low = -0.001
    
    # Adaptive risk management
    base_stop_pct = 0.08
    atr_multiplier = 2.0
    time_stop_bars = 30
    
    # Position sizing
    base_position_size = 0.6
    max_position_size = 0.9
    
    # Volatility regime thresholds
    high_vol_threshold = 0.8
    low_vol_threshold = 0.3

    def init(self):
        close = self.data.Close
        high = self.data.High
        low = self.data.Low
        volume = self.data.Volume
        
        # Quantum momentum system
        self.quantum_score = self.I(QuantumMomentum, close, volume)
        
        # Adaptive risk management
        self.atr = self.I(ATR, high, low, close)
        self.volatility_regime = self.I(VolatilityRegime, close)
        
        # Trade tracking
        self.entry_bar = 0

    def next(self):
        current_bar = len(self.data) - 1
        
        # Current market conditions
        current_quantum = self.quantum_score[-1] if not np.isnan(self.quantum_score[-1]) else 0
        current_vol_regime = self.volatility_regime[-1] if not np.isnan(self.volatility_regime[-1]) else 0.5
        current_atr = self.atr[-1] if not np.isnan(self.atr[-1]) else self.data.Close[-1] * 0.02
        
        # Adaptive thresholds based on volatility regime
        if current_vol_regime > self.high_vol_threshold:
            # High volatility - more conservative
            quantum_entry_threshold = self.quantum_threshold_high * 1.5
            position_size = self.base_position_size * 0.7
            stop_multiplier = 1.5
        elif current_vol_regime < self.low_vol_threshold:
            # Low volatility - more aggressive
            quantum_entry_threshold = self.quantum_threshold_high * 0.7
            position_size = self.max_position_size
            stop_multiplier = 2.5
        else:
            # Medium volatility - standard approach
            quantum_entry_threshold = self.quantum_threshold_high
            position_size = self.base_position_size
            stop_multiplier = self.atr_multiplier
        
        # Entry logic
        if not self.position and current_quantum > quantum_entry_threshold:
            entry_price = self.data.Close[-1]
            
            # Adaptive stop loss
            atr_stop = entry_price - (current_atr * stop_multiplier)
            pct_stop = entry_price * (1 - self.base_stop_pct)
            stop_loss = max(atr_stop, pct_stop)  # Use the less aggressive stop
            
            # Dynamic take profit based on quantum score strength
            tp_multiplier = min(4.0, max(2.0, current_quantum * 1000))
            take_profit = entry_price * (1 + self.base_stop_pct * tp_multiplier)
            
            self.buy(size=position_size, sl=stop_loss, tp=take_profit)
            self.entry_bar = current_bar
        
        # Exit logic
        elif self.position:
            # Time-based exit
            bars_in_trade = current_bar - self.entry_bar
            if bars_in_trade > self.time_stop_bars:
                self.position.close()
            
            # Quantum score deterioration
            elif current_quantum < self.quantum_threshold_low:
                self.position.close()
            
            # Volatility regime shift exit
            elif (current_vol_regime > 0.9 and 
                  self.volatility_regime[-2] < 0.8):  # Sudden volatility spike
                self.position.close()