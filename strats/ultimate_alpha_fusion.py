from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd
import numpy as np


def UltimateAlphaScore(close, high, low, volume):
    """Ultimate Alpha Score combining top 3 strategy elements"""
    close_series = pd.Series(close)
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    volume_series = pd.Series(volume)
    
    # === MACD MOMENTUM COMPONENT (from 646,606% winner) ===
    ema_fast = close_series.ewm(span=8).mean()
    ema_slow = close_series.ewm(span=21).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=9).mean()
    macd_strength = (macd - macd_signal) / close_series
    
    # === RSI MEAN REVERSION COMPONENT (from 2,202% winner) ===
    delta = close_series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    rsi_normalized = (rsi - 50) / 50  # -1 to 1 scale
    
    # === MULTI-TIMEFRAME MOMENTUM (from 259% winner) ===
    ema_8 = close_series.ewm(span=8).mean()
    ema_21 = close_series.ewm(span=21).mean()
    ema_55 = close_series.ewm(span=55).mean()
    ema_144 = close_series.ewm(span=144).mean()
    
    # Perfect alignment bonus
    alignment_score = ((ema_8 > ema_21) & (ema_21 > ema_55) & (ema_55 > ema_144)).astype(float)
    trend_strength = (close_series / ema_144 - 1) * 10  # Trend strength
    
    # === QUANTUM VOLUME ANALYSIS ===
    volume_ma = volume_series.rolling(20).mean()
    volume_surge = volume_series / volume_ma
    price_volume_momentum = (close_series.pct_change(3) * volume_surge).rolling(5).mean()
    
    # === VOLATILITY ADAPTATION ===
    returns = close_series.pct_change()
    volatility = returns.rolling(21).std()
    vol_percentile = volatility.rolling(63).rank(pct=True)
    volatility_factor = np.where(vol_percentile > 0.8, 0.5, 
                                np.where(vol_percentile < 0.2, 1.5, 1.0))
    
    # === ATR FOR MARKET STRUCTURE ===
    tr1 = high_series - low_series
    tr2 = np.abs(high_series - close_series.shift())
    tr3 = np.abs(low_series - close_series.shift())
    atr = np.maximum(tr1, np.maximum(tr2, tr3)).rolling(14).mean()
    atr_normalized = atr / close_series
    
    # === ULTIMATE FUSION FORMULA ===
    ultimate_score = (
        macd_strength * 0.35 +           # Momentum leader
        rsi_normalized * -0.25 +         # Mean reversion (inverted)
        alignment_score * 0.15 +         # Multi-timeframe bonus
        trend_strength * 0.10 +          # Long-term trend
        price_volume_momentum * 0.10 +   # Volume confirmation
        atr_normalized * -0.05           # Volatility adjustment
    ) * volatility_factor
    
    return ultimate_score.values, macd_strength.values, rsi.values, alignment_score.values


class UltimateAlphaFusion(Strategy):
    name = 'Ultimate Alpha Fusion'
    
    # Dynamic thresholds
    alpha_entry_threshold = 0.008
    alpha_exit_threshold = -0.003
    
    # RSI confirmation levels
    rsi_oversold = 35
    rsi_overbought = 75
    
    # Risk management
    base_position_size = 0.85
    max_position_size = 1.0
    stop_loss_base = 0.10
    take_profit_mult = 2.5
    max_trade_duration = 45
    
    def init(self):
        close = self.data.Close
        high = self.data.High
        low = self.data.Low
        volume = self.data.Volume
        
        # Ultimate alpha system
        self.alpha_score, self.macd_strength, self.rsi, self.alignment = self.I(
            UltimateAlphaScore, close, high, low, volume)
        
        # Trade tracking
        self.entry_bar = 0
        self.consecutive_wins = 0
        self.consecutive_losses = 0

    def next(self):
        current_bar = len(self.data) - 1
        
        # Current signals
        alpha = self.alpha_score[-1] if not np.isnan(self.alpha_score[-1]) else 0
        macd_mom = self.macd_strength[-1] if not np.isnan(self.macd_strength[-1]) else 0
        rsi = self.rsi[-1] if not np.isnan(self.rsi[-1]) else 50
        alignment = self.alignment[-1] if not np.isnan(self.alignment[-1]) else 0
        
        # Adaptive position sizing based on recent performance
        if self.consecutive_wins >= 2:
            position_size = min(self.max_position_size, self.base_position_size * 1.2)
        elif self.consecutive_losses >= 2:
            position_size = self.base_position_size * 0.6
        else:
            position_size = self.base_position_size
        
        # Dynamic alpha threshold based on market conditions
        if alignment == 1.0:  # Perfect alignment
            entry_threshold = self.alpha_entry_threshold * 0.7  # More aggressive
        elif rsi < self.rsi_oversold:  # Oversold
            entry_threshold = self.alpha_entry_threshold * 0.8
        else:
            entry_threshold = self.alpha_entry_threshold
        
        # Entry logic: Ultimate alpha signal
        if not self.position and alpha > entry_threshold:
            entry_price = self.data.Close[-1]
            
            # Adaptive stop loss
            if alignment == 1.0:  # Perfect trend - wider stops
                stop_pct = self.stop_loss_base * 1.3
            else:  # Normal conditions
                stop_pct = self.stop_loss_base
                
            stop_loss = entry_price * (1 - stop_pct)
            take_profit = entry_price * (1 + stop_pct * self.take_profit_mult)
            
            # Enhanced position sizing for strong signals
            if alpha > entry_threshold * 1.5 and macd_mom > 0.005:
                final_position_size = min(self.max_position_size, position_size * 1.1)
            else:
                final_position_size = position_size
            
            self.buy(size=final_position_size, sl=stop_loss, tp=take_profit)
            self.entry_bar = current_bar
        
        # Exit logic
        elif self.position:
            bars_in_trade = current_bar - self.entry_bar
            
            # Alpha deterioration exit
            if alpha < self.alpha_exit_threshold:
                self.position.close()
                self._update_performance_tracking(False)  # Assume loss for early exit
            
            # Overbought RSI exit
            elif rsi > self.rsi_overbought and alignment < 1.0:
                self.position.close()
                self._update_performance_tracking(True)   # Assume profit
            
            # Time-based exit
            elif bars_in_trade > self.max_trade_duration:
                self.position.close()
                self._update_performance_tracking(False)  # Neutral
    
    def _update_performance_tracking(self, was_winner):
        """Track consecutive wins/losses for adaptive sizing"""
        if was_winner:
            self.consecutive_wins += 1
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1
            self.consecutive_wins = 0