from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd


def MACD(close, fast=12, slow=26, signal=9):
    """Calculate MACD, signal line, and histogram"""
    close = pd.Series(close)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line.values, signal_line.values, histogram.values


class MACDMomentum(Strategy):
    name = 'MACD Momentum'
    fast_period = 12
    slow_period = 26
    signal_period = 9

    def init(self):
        close = self.data.Close
        self.macd, self.signal, self.histogram = self.I(MACD, close, self.fast_period, self.slow_period, self.signal_period)

    def next(self):
        if crossover(self.macd, self.signal) and not self.position:
            self.buy()
        elif crossover(self.signal, self.macd) and self.position:
            self.position.close()