from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd
import numpy as np


def EMA(close, n):
    """Exponential Moving Average"""
    close = pd.Series(close)
    return close.ewm(span=n).mean().values


def MACD(close, fast=8, slow=21, signal=9):
    """Faster MACD for crypto volatility"""
    close = pd.Series(close)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    return macd_line.values, signal_line.values


def RSI(close, n=14):
    """RSI for mean reversion signals"""
    close = pd.Series(close)
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=n).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=n).mean()
    rs = gain / loss
    return (100 - (100 / (1 + rs))).values


def StochasticRSI(rsi, n=14):
    """Stochastic RSI for additional confirmation"""
    rsi_series = pd.Series(rsi)
    rsi_min = rsi_series.rolling(window=n).min()
    rsi_max = rsi_series.rolling(window=n).max()
    return ((rsi_series - rsi_min) / (rsi_max - rsi_min) * 100).values


class MultiTimeframeMomentum(Strategy):
    name = 'Multi Timeframe Momentum'
    
    # Multi-timeframe EMAs
    ema_fast = 8
    ema_medium = 21
    ema_slow = 55
    ema_trend = 144  # Long-term trend
    
    # Oscillators
    rsi_period = 14
    stoch_period = 14
    
    # Signal thresholds
    rsi_oversold = 30
    rsi_overbought = 70
    stoch_oversold = 20
    stoch_overbought = 80
    
    # Risk management
    position_size = 0.95
    stop_loss_pct = 0.12
    take_profit_mult = 3.0

    def init(self):
        close = self.data.Close
        
        # Multi-timeframe trend analysis
        self.ema_fast = self.I(EMA, close, self.ema_fast)
        self.ema_medium = self.I(EMA, close, self.ema_medium)
        self.ema_slow = self.I(EMA, close, self.ema_slow)
        self.ema_trend = self.I(EMA, close, self.ema_trend)
        
        # Momentum indicators
        self.macd, self.macd_signal = self.I(MACD, close)
        self.rsi = self.I(RSI, close, self.rsi_period)
        self.stoch_rsi = self.I(StochasticRSI, self.rsi, self.stoch_period)

    def next(self):
        # Multi-timeframe trend analysis
        perfect_alignment = (self.ema_fast[-1] > self.ema_medium[-1] > 
                           self.ema_slow[-1] > self.ema_trend[-1])
        
        strong_trend = (self.ema_fast[-1] > self.ema_medium[-1] and 
                       self.ema_medium[-1] > self.ema_slow[-1])
        
        long_term_bullish = self.data.Close[-1] > self.ema_trend[-1]
        
        # Momentum signals
        macd_bullish = self.macd[-1] > self.macd_signal[-1]
        macd_crossover = crossover(self.macd, self.macd_signal)
        
        # Mean reversion signals
        rsi_oversold_signal = self.rsi[-1] < self.rsi_oversold
        stoch_oversold_signal = self.stoch_rsi[-1] < self.stoch_oversold
        
        # Exit signals
        rsi_overbought_signal = self.rsi[-1] > self.rsi_overbought
        stoch_overbought_signal = self.stoch_rsi[-1] > self.stoch_overbought
        
        # Entry logic - Multiple signal combinations
        if not self.position:
            entry_price = self.data.Close[-1]
            stop_loss = entry_price * (1 - self.stop_loss_pct)
            take_profit = entry_price * (1 + self.stop_loss_pct * self.take_profit_mult)
            
            # Strongest signal: Perfect alignment + momentum
            if (perfect_alignment and macd_crossover and long_term_bullish):
                self.buy(size=self.position_size, sl=stop_loss, tp=take_profit)
                
            # Strong signal: Good trend + momentum confluence
            elif (strong_trend and macd_bullish and long_term_bullish and 
                  self.macd[-1] > 0):
                self.buy(size=self.position_size * 0.75, sl=stop_loss, tp=take_profit)
                
            # Mean reversion in uptrend
            elif (long_term_bullish and strong_trend and 
                  rsi_oversold_signal and stoch_oversold_signal):
                self.buy(size=self.position_size * 0.6, sl=stop_loss, tp=take_profit)
        
        # Exit logic
        elif self.position:
            # Take profits on overbought conditions
            if (rsi_overbought_signal and stoch_overbought_signal):
                self.position.close()
            
            # Exit on trend reversal
            elif (self.ema_fast[-1] < self.ema_medium[-1] and 
                  crossover(self.macd_signal, self.macd)):
                self.position.close()