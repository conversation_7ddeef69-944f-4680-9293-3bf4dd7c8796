from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd


def MACD(close, fast=12, slow=26, signal=9):
    """Calculate MACD, signal line, and histogram"""
    close = pd.Series(close)
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line.values, signal_line.values, histogram.values


class MACDMomentumHTF(Strategy):
    name = 'MACD Momentum'

    fast_period_ltf = 5
    slow_period_ltf = 12
    signal_period_ltf = 1

    fast_period_htf = 12
    slow_period_htf = 26
    signal_period_htf = 9

    def init(self):
        close = self.data.Close
        self.macd_ltf, self.signal_ltf, self.histogram_ltf = MACD(close, self.fast_period_ltf, self.slow_period_ltf, self.signal_period_ltf)
        self.macd_htf, self.signal_htf, self.histogram_htf = MACD(close, self.fast_period_htf, self.slow_period_htf, self.signal_period_htf)

    def next(self):
        if crossover(self.macd_htf, self.signal_htf) and not self.position:
            if not crossover(self.macd_ltf, self.signal_ltf):
                self.buy()
        
        elif crossover(self.signal_htf, self.macd_htf) and self.position:
            if not crossover(self.signal_ltf, self.macd_ltf):
                self.sell()


