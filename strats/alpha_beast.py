from backtesting import Strategy
from backtesting.lib import crossover
import pandas as pd
import numpy as np


def AlphaBeastSignal(close, volume):
    """Simplified but powerful alpha signal"""
    close_series = pd.Series(close)
    volume_series = pd.Series(volume)
    
    # Fast MACD for crypto (from 646,606% winner)
    ema_8 = close_series.ewm(span=8).mean()
    ema_21 = close_series.ewm(span=21).mean()
    macd = ema_8 - ema_21
    macd_signal = macd.ewm(span=6).mean()
    macd_momentum = macd - macd_signal
    
    # RSI for timing (from 2,202% winner)
    delta = close_series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # Trend strength (from 259% multi-timeframe winner)
    ema_55 = close_series.ewm(span=55).mean()
    trend_strength = (close_series - ema_55) / ema_55
    
    # Volume surge detection
    volume_ma = volume_series.rolling(20).mean()
    volume_ratio = volume_series / volume_ma
    
    # Beast mode signal: All systems aligned
    beast_signal = (
        macd_momentum / close_series * 1000 +  # MACD momentum (normalized)
        (50 - rsi) / 50 +                      # RSI mean reversion (inverted)
        trend_strength +                       # Trend alignment
        (volume_ratio - 1) * 0.3               # Volume confirmation
    )
    
    return beast_signal.values, macd_momentum.values, rsi.values, trend_strength.values


class AlphaBeast(Strategy):
    name = 'Alpha Beast'
    
    # Signal thresholds
    beast_entry = 0.15
    beast_exit = -0.08
    
    # RSI filters
    rsi_max_entry = 65  # Don't buy when too overbought
    rsi_min_exit = 75   # Exit when very overbought
    
    # Risk management
    position_size = 0.9
    stop_loss_pct = 0.12
    take_profit_mult = 3.0
    
    def init(self):
        close = self.data.Close
        volume = self.data.Volume
        
        self.beast_signal, self.macd_mom, self.rsi, self.trend = self.I(
            AlphaBeastSignal, close, volume)

    def next(self):
        # Current signals
        beast = self.beast_signal[-1] if not np.isnan(self.beast_signal[-1]) else 0
        rsi = self.rsi[-1] if not np.isnan(self.rsi[-1]) else 50
        trend = self.trend[-1] if not np.isnan(self.trend[-1]) else 0
        macd_mom = self.macd_mom[-1] if not np.isnan(self.macd_mom[-1]) else 0
        
        # Entry: Beast signal with RSI filter
        if not self.position and beast > self.beast_entry and rsi < self.rsi_max_entry:
            entry_price = self.data.Close[-1]
            
            # Adaptive stop loss based on trend strength
            if trend > 0.1:  # Strong uptrend - wider stop
                stop_pct = self.stop_loss_pct * 1.4
            else:
                stop_pct = self.stop_loss_pct
            
            stop_loss = entry_price * (1 - stop_pct)
            take_profit = entry_price * (1 + stop_pct * self.take_profit_mult)
            
            # Larger position for stronger signals
            size = self.position_size
            if beast > self.beast_entry * 1.5 and macd_mom > 0:
                size = min(0.95, size * 1.1)
            
            self.buy(size=size, sl=stop_loss, tp=take_profit)
        
        # Exit conditions
        elif self.position:
            # Beast signal deterioration
            if beast < self.beast_exit:
                self.position.close()
            
            # Overbought exit
            elif rsi > self.rsi_min_exit:
                self.position.close()
                
            # Trend reversal exit
            elif trend < -0.05 and macd_mom < 0:
                self.position.close()